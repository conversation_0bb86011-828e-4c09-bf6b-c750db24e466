import * as path from 'path'
import { app } from 'electron'
import { TradingSettings, BotConfig } from '../../../shared/types'

// Import the bot dynamically to avoid compilation issues
let TradingBot: any = null
let createBot: any = null

// Dynamically import the bot
async function loadBot() {
	if (!TradingBot) {
		try {
			// Calculate the absolute path to the bot module
			const botModulePath = path.resolve(__dirname, '../../../../../bot/dist/apps/bot/src/index.js')
			console.log('Attempting to load bot module from:', botModulePath)
			const botModule = await import(botModulePath)
			console.log('Bot module imported:', Object.keys(botModule))
			TradingBot = botModule.TradingBot
			createBot = botModule.createBot
			console.log('TradingBot:', typeof TradingBot)
			console.log('createBot:', typeof createBot)

			if (!TradingBot || !createBot) {
				throw new Error('TradingBot or createBot not found in module')
			}
		} catch (error) {
			console.error('Failed to load bot module:', error)
			const errorMessage = error instanceof Error ? error.message : String(error)
			console.error('Error details:', errorMessage)
			throw new Error(`Bot module not available: ${errorMessage}`)
		}
	}
}

export class BotManager {
	private tradingBot: any = null
	private browserInstance: any = null
	private browserPage: any = null
	private eventCallbacks: { [key: string]: Function[] } = {}
	private browserInitialized: boolean = false
	private userLoggedIn: boolean = false

	constructor() {
		// Pre-load the bot module
		loadBot().catch(console.error)
	}

	// Event emitter methods
	on(event: string, callback: Function) {
		if (!this.eventCallbacks[event]) {
			this.eventCallbacks[event] = []
		}
		this.eventCallbacks[event].push(callback)
	}

	emit(event: string, data: any) {
		if (this.eventCallbacks[event]) {
			this.eventCallbacks[event].forEach(callback => callback(data))
		}
	}

	async initializeBrowser(): Promise<{ success: boolean; message: string }> {
		if (this.browserInitialized) {
			return { success: true, message: 'Browser already initialized' }
		}

		try {
			console.log('Initializing browser...')
			await loadBot()

			const { chromium } = await import('playwright')

			// Launch persistent browser context with session persistence
			const userDataDir = path.join(app.getPath('userData'), 'browser-session')
			this.browserInstance = await chromium.launchPersistentContext(userDataDir, {
				headless: false,
				viewport: null // Use default viewport
			})

			// Get the first page or create a new one
			const pages = this.browserInstance.pages()
			this.browserPage = pages.length > 0 ? pages[0] : await this.browserInstance.newPage()

			// Navigate to Pocket Option
			console.log('Navigating to Pocket Option...')
			await this.browserPage.goto('https://pocketoption.com/en/cabinet/demo-quick-high-low/')

			// Wait for initial page load
			await this.browserPage.waitForLoadState('domcontentloaded', { timeout: 30000 })

			this.browserInitialized = true

			// Check if user is already logged in
			await this.checkLoginStatus()

			console.log('Browser initialized successfully')
			return { success: true, message: 'Browser initialized successfully. Please log in if needed.' }
		} catch (error) {
			console.error('Failed to initialize browser:', error)
			await this.closeBrowser()
			return { success: false, message: `Failed to initialize browser: ${error}` }
		}
	}

	async checkLoginStatus(): Promise<{ success: boolean; loggedIn: boolean; message: string }> {
		if (!this.browserPage) {
			return { success: false, loggedIn: false, message: 'Browser not initialized' }
		}

		try {
			// Check for login indicators
			const loginIndicators = [
				'.user-info',
				'.account-balance',
				'.balance-item',
				'.user-menu',
				'.trading-interface',
				'.demo-account',
				'.real-account'
			]

			let loggedIn = false
			for (const selector of loginIndicators) {
				try {
					const element = await this.browserPage.$(selector)
					if (element) {
						loggedIn = true
						break
					}
				} catch (e) {
					// Continue checking
				}
			}

			this.userLoggedIn = loggedIn
			const message = loggedIn ? 'User is logged in' : 'User needs to log in'

			return { success: true, loggedIn, message }
		} catch (error) {
			console.error('Failed to check login status:', error)
			return { success: false, loggedIn: false, message: `Failed to check login status: ${error}` }
		}
	}

	async closeBrowser(): Promise<{ success: boolean; message: string }> {
		try {
			if (this.browserInstance) {
				await this.browserInstance.close()
				this.browserInstance = null
				this.browserPage = null
			}
			this.browserInitialized = false
			this.userLoggedIn = false
			return { success: true, message: 'Browser closed successfully' }
		} catch (error) {
			console.error('Failed to close browser:', error)
			return { success: false, message: `Failed to close browser: ${error}` }
		}
	}

	getBrowserStatus(): { initialized: boolean; loggedIn: boolean } {
		return {
			initialized: this.browserInitialized,
			loggedIn: this.userLoggedIn
		}
	}

	async startBot(settings: TradingSettings): Promise<{ success: boolean; message: string }> {
		if (this.tradingBot && this.tradingBot.getStatus().isRunning) {
			return { success: false, message: 'Bot is already running' }
		}

		// Check if browser is initialized and user is logged in
		if (!this.browserInitialized) {
			return { success: false, message: 'Please initialize browser first' }
		}

		if (!this.userLoggedIn) {
			// Re-check login status
			const loginCheck = await this.checkLoginStatus()
			if (!loginCheck.loggedIn) {
				return { success: false, message: 'Please log in to Pocket Option first' }
			}
		}

		try {
			// Ensure bot module is loaded
			console.log('Loading bot module...')
			await loadBot()
			console.log('Bot module loaded successfully')

			console.log('Starting bot with settings:', settings)

			// Create new bot instance with existing browser
			console.log('Creating bot instance...')
			this.tradingBot = createBot(settings, {
				headless: false,
				userDataDir: path.join(app.getPath('userData'), 'browser-session'),
				existingBrowser: this.browserInstance,
				existingPage: this.browserPage
			})
			console.log('Bot instance created')

			// Set up event listeners to forward events
			this.tradingBot.on('status-update', (status: any) => {
				this.emit('status-update', status)
			})

			this.tradingBot.on('price-update', (priceData: any) => {
				this.emit('price-update', priceData)
			})

			this.tradingBot.on('trade-result', (result: any) => {
				this.emit('trade-result', result)
			})

			this.tradingBot.on('log', (logEntry: any) => {
				console.log(`[BOT ${logEntry.level.toUpperCase()}] ${logEntry.message}`)
				this.emit('log', logEntry)
			})

			this.tradingBot.on('error', (error: any) => {
				console.error('[BOT ERROR]', error)
				this.emit('error', error)
			})

			// Start the bot
			console.log('Starting bot...')
			await this.tradingBot.start()
			console.log('Bot started successfully!')

			return { success: true, message: 'Bot started successfully' }
		} catch (error) {
			console.error('Failed to start bot:', error)
			return { success: false, message: `Failed to start bot: ${error}` }
		}
	}

	async stopBot(): Promise<{ success: boolean; message: string }> {
		if (!this.tradingBot) {
			return { success: false, message: 'No bot instance found' }
		}

		try {
			await this.tradingBot.stop()
			this.tradingBot.removeAllListeners()
			this.tradingBot = null

			// Keep browser open for future use
			console.log('Bot stopped, browser remains open for reuse')
			return { success: true, message: 'Bot stopped successfully' }
		} catch (error) {
			console.error('Failed to stop bot:', error)
			return { success: false, message: `Failed to stop bot: ${error}` }
		}
	}

	getBotStatus() {
		if (!this.tradingBot) {
			return {
				isRunning: false,
				currentPrice: 0,
				tradesCount: 0,
				winCount: 0,
				lossCount: 0,
				winRate: 0,
				totalProfit: 0,
				lastTrade: null
			}
		}

		return this.tradingBot.getStatus()
	}

	updateSettings(settings: TradingSettings): { success: boolean; message: string } {
		if (this.tradingBot) {
			this.tradingBot.updateSettings(settings)
			return { success: true, message: 'Settings updated successfully' }
		}
		return { success: false, message: 'No bot instance found' }
	}

	// TODO: Remove this on production
	async debugPriceElements(): Promise<{ success: boolean; message: string }> {
		if (!this.tradingBot) {
			return { success: false, message: 'Bot not running' }
		}

		try {
			await this.tradingBot.debugPriceElements()
			return { success: true, message: 'Debug price elements executed' }
		} catch (error) {
			return { success: false, message: `Failed to debug price elements: ${error}` }
		}
	}

	// TODO: Remove this on production
	async selectTradingAsset(): Promise<{ success: boolean; message: string }> {
		if (!this.tradingBot) {
			return { success: false, message: 'Bot not running' }
		}

		try {
			await this.tradingBot.selectTradingAsset()
			return { success: true, message: 'Select trading asset executed' }
		} catch (error) {
			return { success: false, message: `Failed to select trading asset: ${error}` }
		}
	}
}

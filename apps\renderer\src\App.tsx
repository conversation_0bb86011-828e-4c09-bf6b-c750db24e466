import { useState, useEffect } from 'react'
import { TradingDashboard } from './components/TradingDashboard'
import { SettingsPanel } from './components/SettingsPanel'
import type { TradingSettings, BotStatus, PriceData, TradeResult } from '../../../shared/types'

// Extend Window interface for TypeScript
declare global {
	interface Window {
		electronAPI: {
			getSettings: () => Promise<TradingSettings>
			saveSettings: (settings: TradingSettings) => Promise<boolean>
			startBot: (settings: TradingSettings) => Promise<{ success: boolean; message: string }>
			stopBot: () => Promise<{ success: boolean; message: string }>
			getBotStatus: () => Promise<BotStatus>
			updateBotSettings: (settings: TradingSettings) => Promise<{ success: boolean; message: string }>
			initializeBrowser: () => Promise<{ success: boolean; message: string }>
			checkLoginStatus: () => Promise<{ success: boolean; loggedIn: boolean; message: string }>
			closeBrowser: () => Promise<{ success: boolean; message: string }>
			getBrowserStatus: () => Promise<{ initialized: boolean; loggedIn: boolean }>
			onBotStatusUpdate: (callback: (status: BotStatus) => void) => void
			onPriceUpdate: (callback: (price: PriceData) => void) => void
			onTradeResult: (callback: (result: TradeResult) => void) => void
			onBotStatusMessage: (callback: (message: string) => void) => void
			showErrorDialog: (title: string, content: string) => void
			showInfoDialog: (title: string, content: string) => void
			removeAllListeners: (channel: string) => void

			// TODO: Remove this on production
			debugPriceElements: () => void
			selectTradingAsset: () => void
		}
	}
}

function App() {
	const [settings, setSettings] = useState<TradingSettings>({
		threshold: 0.02,
		tradeAmount: 1,
		autoTrade: false,
		maxTrades: 10
	})

	const [botStatus, setBotStatus] = useState<BotStatus>({
		isRunning: false,
		currentPrice: 0,
		tradesCount: 0,
		winCount: 0,
		lossCount: 0,
		winRate: 0,
		totalProfit: 0,
		lastTrade: null
	})

	const [currentPrice, setCurrentPrice] = useState<PriceData | null>(null)
	const [recentTrades, setRecentTrades] = useState<TradeResult[]>([])
	const [showSettings, setShowSettings] = useState(false)
	const [statusMessage, setStatusMessage] = useState<string>('')
	const [browserStatus, setBrowserStatus] = useState<{ initialized: boolean; loggedIn: boolean }>({
		initialized: false,
		loggedIn: false
	})

	// Load settings and browser status on component mount
	useEffect(() => {
		const loadSettings = async () => {
			try {
				const savedSettings = await window.electronAPI.getSettings()
				setSettings(savedSettings)
			} catch (error) {
				console.error('Failed to load settings:', error)
			}
		}

		const loadBrowserStatus = async () => {
			try {
				const status = await window.electronAPI.getBrowserStatus()
				setBrowserStatus(status)
			} catch (error) {
				console.error('Failed to load browser status:', error)
			}
		}

		loadSettings()
		loadBrowserStatus()
	}, [])

	// Set up event listeners
	useEffect(() => {
		// Bot status updates
		window.electronAPI.onBotStatusUpdate((status: BotStatus) => {
			setBotStatus(status)
		})

		// Price updates
		window.electronAPI.onPriceUpdate((priceData: PriceData) => {
			setCurrentPrice(priceData)
		})

		// Trade results
		window.electronAPI.onTradeResult((result: TradeResult) => {
			setRecentTrades(prev => [result, ...prev.slice(0, 9)]) // Keep last 10 trades
		})

		// Bot status messages
		window.electronAPI.onBotStatusMessage((message: string) => {
			setStatusMessage(message)
			// Clear message after 10 seconds
			setTimeout(() => setStatusMessage(''), 10000)
		})

		// Cleanup listeners on unmount
		return () => {
			window.electronAPI.removeAllListeners('bot-status-update')
			window.electronAPI.removeAllListeners('price-update')
			window.electronAPI.removeAllListeners('trade-result')
			window.electronAPI.removeAllListeners('bot-status-message')
		}
	}, [])

	const handleStartBot = async () => {
		try {
			const result = await window.electronAPI.startBot(settings)
			if (result.success) {
				window.electronAPI.showInfoDialog('Success', result.message)
			} else {
				window.electronAPI.showErrorDialog('Error', result.message)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to start bot: ${error}`)
		}
	}

	const handleStopBot = async () => {
		try {
			const result = await window.electronAPI.stopBot()
			if (result.success) {
				window.electronAPI.showInfoDialog('Success', result.message)
			} else {
				window.electronAPI.showErrorDialog('Error', result.message)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to stop bot: ${error}`)
		}
	}

	const handleInitializeBrowser = async () => {
		try {
			const result = await window.electronAPI.initializeBrowser()
			if (result.success) {
				setBrowserStatus({ initialized: true, loggedIn: false })
				window.electronAPI.showInfoDialog('Success', result.message)
				// Check login status after initialization
				setTimeout(async () => {
					const loginStatus = await window.electronAPI.checkLoginStatus()
					setBrowserStatus(prev => ({ ...prev, loggedIn: loginStatus.loggedIn }))
				}, 2000)
			} else {
				window.electronAPI.showErrorDialog('Error', result.message)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to initialize browser: ${error}`)
		}
	}

	const handleCheckLoginStatus = async () => {
		try {
			const result = await window.electronAPI.checkLoginStatus()
			setBrowserStatus(prev => ({ ...prev, loggedIn: result.loggedIn }))
			if (result.loggedIn) {
				window.electronAPI.showInfoDialog('Login Status', 'You are logged in to Pocket Option')
			} else {
				window.electronAPI.showInfoDialog('Login Status', 'Please log in to Pocket Option')
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to check login status: ${error}`)
		}
	}

	const handleCloseBrowser = async () => {
		try {
			const result = await window.electronAPI.closeBrowser()
			if (result.success) {
				setBrowserStatus({ initialized: false, loggedIn: false })
				window.electronAPI.showInfoDialog('Success', result.message)
			} else {
				window.electronAPI.showErrorDialog('Error', result.message)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to close browser: ${error}`)
		}
	}

	const handleSaveSettings = async (newSettings: TradingSettings) => {
		try {
			await window.electronAPI.saveSettings(newSettings)
			setSettings(newSettings)

			// Update bot settings if running
			if (botStatus.isRunning) {
				await window.electronAPI.updateBotSettings(newSettings)
			}

			setShowSettings(false)
			window.electronAPI.showInfoDialog('Success', 'Settings saved successfully')
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to save settings: ${error}`)
		}
	}

	// TODO: Remove this on production
	const handleDebugPriceElements = async () => {
		try {
			await window.electronAPI.debugPriceElements()
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to debug price elements: ${error}`)
		}
	}

	// TODO: Remove this on production
	const handleSelectTradingAsset = async () => {
		try {
			await window.electronAPI.selectTradingAsset()
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to select trading asset: ${error}`)
		}
	}

	return (
		<div className="min-h-screen bg-gray-900 text-white">
			<div className="container mx-auto px-4 py-6">
				<header className="flex justify-between items-center mb-8">
					<h1 className="text-3xl font-bold text-blue-400">Pocket Option Trading Bot</h1>
					<button
						onClick={() => setShowSettings(!showSettings)}
						className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
					>
						Settings
					</button>
				</header>

				{showSettings ? (
					<SettingsPanel settings={settings} onSave={handleSaveSettings} onCancel={() => setShowSettings(false)} />
				) : (
					<TradingDashboard
						botStatus={botStatus}
						currentPrice={currentPrice}
						recentTrades={recentTrades}
						onStartBot={handleStartBot}
						onStopBot={handleStopBot}
						statusMessage={statusMessage}
						browserStatus={browserStatus}
						onInitializeBrowser={handleInitializeBrowser}
						onCheckLoginStatus={handleCheckLoginStatus}
						onCloseBrowser={handleCloseBrowser}
						// TODO: Remove these on production
						onDebugPriceElements={handleDebugPriceElements}
						onSelectTradingAsset={handleSelectTradingAsset}
					/>
				)}
			</div>
		</div>
	)
}

export default App

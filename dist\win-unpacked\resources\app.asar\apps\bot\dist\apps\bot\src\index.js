"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingBot = void 0;
exports.createBot = createBot;
const TradingBot_1 = require("./TradingBot");
Object.defineProperty(exports, "TradingBot", { enumerable: true, get: function () { return TradingBot_1.TradingBot; } });
__exportStar(require("../../../shared/types"), exports);
// Factory function to create a bot instance
function createBot(settings, options) {
    const config = {
        settings,
        pocketOptionUrl: 'https://pocketoption.com/en/cabinet/demo-quick-high-low/',
        headless: false, // Set to true for production
        userDataDir: undefined, // Can be set to persist browser session
        ...options
    };
    return new TradingBot_1.TradingBot(config);
}
// Default export for easy importing
exports.default = TradingBot_1.TradingBot;
//# sourceMappingURL=index.js.map
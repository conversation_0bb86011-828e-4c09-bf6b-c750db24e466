@import 'tailwindcss';

* {
	box-sizing: border-box;
}

body {
	margin: 0;
	font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell',
		'Open Sans', 'Helvetica Neue', sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	background-color: #111827;
	color: #ffffff;
}

#root {
	min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: #374151;
}

::-webkit-scrollbar-thumb {
	background: #6b7280;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #9ca3af;
}

# AI Trading Bot for Pocket Option

## 🧱 Project Structure

pocket-bot/
├── apps/
│ ├── main/ # Electron main process
│ ├── renderer/ # React UI (Vite + Tailwind)
│ └── bot/ # Playwright trading logic
├── shared/ # Shared types, config, utils
├── package.json # Monorepo root config
├── pnpm-workspace.yaml # (if using pnpm)

---

## 🧰 Tools Used

| Layer            | Tool                        | Purpose                                      |
| ---------------- | --------------------------- | -------------------------------------------- |
| Automation       | **Playwright (Node.js)**    | Controls Chrome; reads price, clicks buttons |
| GUI              | **Electron + React + Vite** | Desktop UI: Start/Stop bot, set thresholds   |
| Styling          | **Tailwind CSS**            | Styling UI components                        |
| State Management | **Electron Store**          | Persisting user settings and preferences     |
| Build Tool       | **Electron Builder**        | Packaging the app for distribution           |

---

## 🚀 Getting Started

1. Clone the Repository:

   ```bash
   Copy
   Edit
   git clone https://github.com/your-username/pocket-bot.git
   cd pocket-bot
   ```

2. Install Dependencies:

   ```bash
   Copy
   Edit
   npm install
   ```

3. Run the App in Development Mode:

   ```bash
   Copy
   Edit
   npm run dev
   ```

4. Build for Production:

   ```bash
   Copy
   Edit
   npm run build
   ```

---

## 🧠 Bot Workflow

1. User logs in manually to Pocket Option (keep browser session alive).
2. Bot reads price from DOM.
3. Compares to last tick → detect trend (up/down).
4. If rule matches:
   - Click High/Low button.
   - Wait N seconds.
   - Check DOM for result (Win/Loss).
5. Log outcome.

---

## 🖥️ GUI Features

- Input: Trade threshold (e.g., 0.02).
- Controls: Start/Stop bot.
- Display: Current price, trade direction, outcome.
- Settings: Persisted using Electron Store.

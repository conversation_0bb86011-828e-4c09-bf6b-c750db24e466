export interface TradingSettings {
    threshold: number;
    tradeAmount: number;
    autoTrade: boolean;
    maxTrades: number;
    stopLoss?: number;
    takeProfit?: number;
    assetType?: 'currency' | 'cryptocurrency' | 'commodity' | 'stock' | 'index';
    asset?: string;
    assetFilter?: 'OTC' | 'ALL';
}
export interface PriceData {
    current: number;
    previous: number;
    timestamp: number;
    trend: 'up' | 'down' | 'neutral';
    change: number;
    changePercent: number;
}
export interface TradeResult {
    id: string;
    timestamp: number;
    direction: 'high' | 'low';
    amount: number;
    entryPrice: number;
    exitPrice?: number;
    result: 'win' | 'loss' | 'pending';
    profit?: number;
    duration: number;
}
export interface BotStatus {
    isRunning: boolean;
    currentPrice: number;
    tradesCount: number;
    winCount: number;
    lossCount: number;
    winRate: number;
    totalProfit: number;
    lastTrade: TradeResult | null;
    startTime?: number;
    uptime?: number;
}
export interface BotConfig {
    settings: TradingSettings;
    pocketOptionUrl: string;
    headless: boolean;
    userDataDir?: string;
    existingBrowser?: any;
    existingPage?: any;
}
export interface BotEvents {
    'status-update': BotStatus;
    'price-update': PriceData;
    'trade-result': TradeResult;
    error: {
        message: string;
        details?: any;
    };
    log: {
        level: 'info' | 'warn' | 'error';
        message: string;
        timestamp: number;
    };
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
//# sourceMappingURL=types.d.ts.map
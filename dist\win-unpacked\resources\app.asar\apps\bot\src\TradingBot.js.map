{"version": 3, "file": "TradingBot.js", "sourceRoot": "", "sources": ["TradingBot.ts"], "names": [], "mappings": ";;;AAAA,2CAAqD;AACrD,mCAAsC;AAGtC,MAAa,UAAW,SAAQ,qBAAY;IAU1C,YAAY,MAAiB;QAC3B,KAAK,EAAE,CAAC;QAVF,YAAO,GAAmB,IAAI,CAAC;QAC/B,SAAI,GAAgB,IAAI,CAAC;QACzB,cAAS,GAAG,KAAK,CAAC;QAGlB,iBAAY,GAAgB,EAAE,CAAC;QAC/B,WAAM,GAAkB,EAAE,CAAC;QAC3B,uBAAkB,GAA0B,IAAI,CAAC;QAIvD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG;YACZ,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,CAAC;YACf,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,CAAC;YACZ,OAAO,EAAE,CAAC;YACV,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;SAChB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAAC;YAE5C,iBAAiB;YACjB,IAAI,CAAC,OAAO,GAAG,MAAM,qBAAQ,CAAC,MAAM,CAAC;gBACnC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;aACrC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEzC,4BAA4B;YAC5B,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAElD,wBAAwB;YACxB,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAEhD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEnC,yBAAyB;YACzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,kCAAkC,CAAC,CAAC;YACrD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,wBAAwB,KAAK,EAAE,CAAC,CAAC;YACnD,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAAC;QAE5C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;QAE9B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;QACxC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC/C,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,2BAA2B,KAAK,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,2BAA2B;IACvC,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE1C,IAAI,CAAC;YACH,sGAAsG;YACtG,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,qDAAqD,CAAC,CAAC;YAE9F,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAAC;gBAC5C,OAAO;YACT,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,WAAW,EAAE,CAAC;YACnD,MAAM,YAAY,GAAG,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;YAE3E,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC/C,MAAM,SAAS,GAAc;oBAC3B,OAAO,EAAE,YAAY;oBACrB,QAAQ,EAAE,aAAa;oBACvB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,KAAK,EAAE,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;oBAC9F,MAAM,EAAE,YAAY,GAAG,aAAa;oBACpC,aAAa,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,CAAC,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;iBAC9F,CAAC;gBAEF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAElC,kCAAkC;gBAClC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;oBACnC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;gBAC5B,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;gBAErC,kCAAkC;gBAClC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACnC,MAAM,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,yBAAyB,KAAK,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,SAAoB;QACzD,uDAAuD;QACvD,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QAEtD,0CAA0C;QAC1C,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,SAAS,EAAE,CAAC;YACzC,OAAO;QACT,CAAC;QAED,0CAA0C;QAC1C,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,SAAS,EAAE,CAAC;YACnD,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5D,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,SAAyB,EAAE,SAAoB;QACxE,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO;QAEvB,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,SAAS,mBAAmB,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YAE/E,gGAAgG;YAChG,MAAM,cAAc,GAAG,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,oBAAoB,CAAC;YAC5F,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;YAEjD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,SAAS,mBAAmB,CAAC,CAAC;gBACnD,OAAO;YACT,CAAC;YAED,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;YAErB,sBAAsB;YACtB,MAAM,KAAK,GAAgB;gBACzB,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;gBACzB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,SAAS;gBACT,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW;gBACxC,UAAU,EAAE,SAAS,CAAC,OAAO;gBAC7B,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,EAAE,CAAC,yBAAyB;aACvC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;YAE9B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,sFAAsF;YACtF,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAClC,CAAC,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;QAE5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,0BAA0B,KAAK,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAe;QAC5C,sCAAsC;QACtC,sDAAsD;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,sEAAsE;QACtE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;QAClC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;QACtC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC3C,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,aAAa;QAExE,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC;QAC7E,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,OAAO,YAAY,KAAK,CAAC,MAAM,aAAa,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACxF,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACpF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IACjD,CAAC;IAEO,GAAG,CAAC,KAAgC,EAAE,OAAe;QAC3D,MAAM,QAAQ,GAAG;YACf,KAAK;YACL,OAAO;YACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,OAAO,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED,cAAc,CAAC,QAAyB;QACtC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;QACvC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;IACvC,CAAC;CACF;AA7PD,gCA6PC"}
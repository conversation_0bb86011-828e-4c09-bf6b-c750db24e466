/// <reference types="vite/client" />

declare global {
	interface Window {
		electronAPI: {
			getSettings: () => Promise<any>
			saveSettings: (settings: any) => Promise<boolean>
			startBot: (settings: any) => Promise<{ success: boolean; message: string }>
			stopBot: () => Promise<{ success: boolean; message: string }>
			getBotStatus: () => Promise<any>
			updateBotSettings: (settings: any) => Promise<{ success: boolean; message: string }>
			initializeBrowser: () => Promise<{ success: boolean; message: string }>
			checkLoginStatus: () => Promise<{ success: boolean; loggedIn: boolean; message: string }>
			closeBrowser: () => Promise<{ success: boolean; message: string }>
			getBrowserStatus: () => Promise<{ initialized: boolean; loggedIn: boolean }>
			onBotStatusUpdate: (callback: (status: any) => void) => void
			onPriceUpdate: (callback: (price: any) => void) => void
			onTradeResult: (callback: (result: any) => void) => void
			onBotStatusMessage: (callback: (message: string) => void) => void
			showErrorDialog: (title: string, content: string) => void
			showInfoDialog: (title: string, content: string) => void
			removeAllListeners: (channel: string) => void
		}
	}
}

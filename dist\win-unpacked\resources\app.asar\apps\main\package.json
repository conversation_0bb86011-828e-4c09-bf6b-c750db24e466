{"name": "main", "version": "1.0.0", "description": "", "main": "dist/apps/main/src/main.js", "scripts": {"dev": "tsc && electron dist/apps/main/src/main.js", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.6.5", "devDependencies": {"@types/node": "^22.0.0", "typescript": "^5.0.0"}, "dependencies": {"playwright": "^1.48.0"}}